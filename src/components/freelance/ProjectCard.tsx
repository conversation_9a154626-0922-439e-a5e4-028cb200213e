import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { FreelanceProject, STATUS_LABELS, STATUS_COLORS, WORK_LOCATION_LABELS, WORK_LOCATION_COLORS } from '@/types/freelance';
import { Calendar, Building2, User, Mail, Phone, Edit, Trash2, Eye } from 'lucide-react';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';

interface ProjectCardProps {
  project: FreelanceProject;
  onEdit: (project: FreelanceProject) => void;
  onDelete: (id: string) => void;
  onView: (project: FreelanceProject) => void;
}

export const ProjectCard = ({ project, onEdit, onDelete, onView }: ProjectCardProps) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Nicht angegeben';
    try {
      return format(new Date(dateString), 'dd.MM.yyyy', { locale: de });
    } catch {
      return 'Ungültiges Datum';
    }
  };

  return (
    <TooltipProvider>
      <Card className="bg-gradient-card border-border/50 hover:border-primary/30 transition-all duration-300 hover:shadow-glow group w-full h-full flex flex-col">
        <div className="flex flex-col h-full relative">
        
        {/* Status Badge - Absolute Position */}
        <Badge
          variant="outline"
          className={`${STATUS_COLORS[project.status]} text-xs border absolute top-3 right-3 z-20`}
        >
          {STATUS_LABELS[project.status]}
        </Badge>
        
        {/* Action Buttons - Absolute Position */}
        <div className="flex gap-1 opacity-100 sm:opacity-0 sm:group-hover:opacity-100 transition-opacity absolute top-8 sm:top-12 right-3 z-10">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onView(project)}
            className="h-7 w-7 sm:h-8 sm:w-8 p-0 hover:bg-primary/10"
          >
            <Eye className="h-3 w-3 sm:h-4 sm:w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onEdit(project)}
            className="h-7 w-7 sm:h-8 sm:w-8 p-0 hover:bg-primary/10"
          >
            <Edit className="h-3 w-3 sm:h-4 sm:w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDelete(project.id)}
            className="h-7 w-7 sm:h-8 sm:w-8 p-0 hover:bg-destructive/10 text-destructive"
          >
            <Trash2 className="h-3 w-3 sm:h-4 sm:w-4" />
          </Button>
        </div>

        {/* SECTION 1: Project Title - FIXED HEIGHT with proper clearance from status badge */}
        <div className="h-20 px-4 pr-8 pt-10 pb-2 flex items-start">
          <Tooltip>
            <TooltipTrigger asChild>
              <h3 className="font-semibold text-base sm:text-lg text-foreground group-hover:text-primary transition-colors leading-tight overflow-hidden cursor-help w-full">
                <div className="line-clamp-2 break-words">
                  {project.project_name}
                </div>
              </h3>
            </TooltipTrigger>
            <TooltipContent side="top" className="max-w-xs">
              <p>{project.project_name}</p>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* SECTION 2: Recruiter/Company Contact Section - FIXED HEIGHT with proper spacing */}
        <div className="h-32 px-4 py-3 flex flex-col justify-start">
          {/* Section Header with visual separator */}
          <div className="flex items-center gap-2 mb-3 pb-1 border-b border-border/30">
            <Building2 className="h-4 w-4 text-primary flex-shrink-0" />
            <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
              Kontakt & Unternehmen
            </span>
          </div>

          <div className="space-y-2.5 w-full pr-8">
            {/* Company Name - Primary Information */}
            <div className="flex items-start gap-2">
              <div className="w-full">
                <div className="font-semibold text-sm text-foreground leading-tight break-words"
                     title={project.company_name}>
                  {project.company_name}
                </div>
              </div>
            </div>

            {/* Contact Person - Secondary Information */}
            {project.contact_person && (
              <div className="flex items-start gap-2">
                <User className="h-3.5 w-3.5 text-muted-foreground flex-shrink-0 mt-0.5" />
                <div className="text-sm text-muted-foreground leading-tight break-words"
                     title={project.contact_person}>
                  {project.contact_person}
                </div>
              </div>
            )}

            {/* Contact Details - Grouped logically */}
            <div className="space-y-1.5">
              {project.contact_email && (
                <div className="flex items-start gap-2">
                  <Mail className="h-3.5 w-3.5 text-muted-foreground flex-shrink-0 mt-0.5" />
                  <a
                    href={`mailto:${project.contact_email}`}
                    className="text-sm text-muted-foreground hover:text-primary transition-colors leading-tight break-all"
                    title={project.contact_email}
                  >
                    {project.contact_email}
                  </a>
                </div>
              )}
              {project.contact_phone && (
                <div className="flex items-start gap-2">
                  <Phone className="h-3.5 w-3.5 text-muted-foreground flex-shrink-0 mt-0.5" />
                  <div className="text-sm text-muted-foreground leading-tight break-words"
                       title={project.contact_phone}>
                    {project.contact_phone}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* SECTION 3: Work Location Badge - FIXED HEIGHT with proper spacing */}
        <div className="h-10 px-4 py-2 flex items-center">
          {project.work_location_type && (
            <Badge variant="outline" className={`${WORK_LOCATION_COLORS[project.work_location_type]} text-xs`}>
              {WORK_LOCATION_LABELS[project.work_location_type]}
              {project.remote_percentage && ` (${project.remote_percentage}%)`}
            </Badge>
          )}
        </div>

        {/* SECTION 4: Application Date and Budget - FIXED HEIGHT with proper spacing */}
        <div className="h-16 px-4 py-3 flex items-center">
          <div className="grid grid-cols-1 gap-2 text-sm w-full pr-8">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Calendar className="h-4 w-4 flex-shrink-0" />
              <span className="break-words font-medium" title={`Bewerbung: ${formatDate(project.application_date)}`}>
                Bewerbung: {formatDate(project.application_date)}
              </span>
            </div>
            {project.budget_range && (
              <div className="text-muted-foreground">
                <span className="break-words" title={`Budget: ${project.budget_range}`}>
                  <span className="font-medium text-foreground">Budget:</span> {project.budget_range}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* SECTION 5: Project Dates - FIXED HEIGHT with proper spacing */}
        <div className="h-14 px-4 py-3 flex items-center">
          <div className="flex flex-col gap-1.5 text-sm text-muted-foreground w-full pr-8">
            {project.project_start_date && (
              <div className="break-words" title={`Start: ${formatDate(project.project_start_date)}`}>
                <span className="font-medium text-foreground">Start:</span> {formatDate(project.project_start_date)}
              </div>
            )}
            {project.project_end_date && (
              <div className="break-words" title={`Ende: ${formatDate(project.project_end_date)}`}>
                <span className="font-medium text-foreground">Ende:</span> {formatDate(project.project_end_date)}
              </div>
            )}
          </div>
        </div>

        {/* SECTION 6: Skills - FIXED HEIGHT with proper spacing */}
        <div className="h-14 px-4 py-3 flex items-center">
          <div className="w-full pr-8 overflow-hidden">
            {project.required_skills && project.required_skills.length > 0 && (
              <div className="flex flex-wrap gap-1.5 overflow-hidden">
                {project.required_skills.slice(0, 4).map((skill, index) => (
                  <Badge key={index} variant="secondary" className="text-xs whitespace-nowrap">
                    {skill}
                  </Badge>
                ))}
                {project.required_skills.length > 4 && (
                  <Badge
                    variant="outline"
                    className="text-xs cursor-pointer hover:bg-accent whitespace-nowrap"
                    title={`Alle Skills: ${project.required_skills.join(', ')}`}
                  >
                    +{project.required_skills.length - 4}
                  </Badge>
                )}
              </div>
            )}
          </div>
        </div>

        {/* SECTION 7: Project Description - FLEXIBLE HEIGHT with proper spacing */}
        <div className="flex-1 px-4 pb-4 pt-2 flex flex-col">
          {project.project_description && (
            <div className="text-sm text-muted-foreground flex-1 pr-8">
              <div className="overflow-hidden">
                <p
                  className={`${!isExpanded ? 'line-clamp-3' : ''} break-words`}
                  title={!isExpanded ? project.project_description : undefined}
                >
                  {project.project_description}
                </p>
              </div>
              {project.project_description.length > 150 && (
                <Button
                  variant="link"
                  size="sm"
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="h-auto p-0 text-xs text-primary mt-1"
                >
                  {isExpanded ? 'Weniger anzeigen' : 'Mehr anzeigen'}
                </Button>
              )}
            </div>
          )}
        </div>

        </div>
      </Card>
    </TooltipProvider>
  );
};
