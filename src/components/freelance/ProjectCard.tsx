import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { FreelanceProject, STATUS_LABELS, STATUS_COLORS, WORK_LOCATION_LABELS, WORK_LOCATION_COLORS } from '@/types/freelance';
import { Calendar, Building2, User, Mail, Phone, Edit, Trash2, Eye } from 'lucide-react';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';

interface ProjectCardProps {
  project: FreelanceProject;
  onEdit: (project: FreelanceProject) => void;
  onDelete: (id: string) => void;
  onView: (project: FreelanceProject) => void;
}

export const ProjectCard = ({ project, onEdit, onDelete, onView }: ProjectCardProps) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Nicht angegeben';
    try {
      return format(new Date(dateString), 'dd.MM.yyyy', { locale: de });
    } catch {
      return 'Ungültiges Datum';
    }
  };

  return (
    <TooltipProvider>
      <Card className="bg-gradient-card border-border/50 hover:border-primary/30 transition-all duration-300 hover:shadow-glow group w-full h-full flex flex-col">
        <div className="flex flex-col h-full relative">
        
        {/* Status Badge - Absolute Position */}
        <Badge
          variant="outline"
          className={`${STATUS_COLORS[project.status]} text-xs border absolute top-3 right-3 z-20`}
        >
          {STATUS_LABELS[project.status]}
        </Badge>
        
        {/* Action Buttons - Absolute Position */}
        <div className="flex gap-1 opacity-100 sm:opacity-0 sm:group-hover:opacity-100 transition-opacity absolute top-8 sm:top-12 right-3 z-10">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onView(project)}
            className="h-7 w-7 sm:h-8 sm:w-8 p-0 hover:bg-primary/10"
          >
            <Eye className="h-3 w-3 sm:h-4 sm:w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onEdit(project)}
            className="h-7 w-7 sm:h-8 sm:w-8 p-0 hover:bg-primary/10"
          >
            <Edit className="h-3 w-3 sm:h-4 sm:w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDelete(project.id)}
            className="h-7 w-7 sm:h-8 sm:w-8 p-0 hover:bg-destructive/10 text-destructive"
          >
            <Trash2 className="h-3 w-3 sm:h-4 sm:w-4" />
          </Button>
        </div>

        {/* SECTION 1: Project Title - FIXED HEIGHT 5rem mit mehr Abstand */}
        <div className="h-20 p-4 pr-20 pt-8 flex items-start">
          <Tooltip>
            <TooltipTrigger asChild>
              <h3 className="font-semibold text-base sm:text-lg text-foreground group-hover:text-primary transition-colors leading-tight overflow-hidden cursor-help">
                <div className="line-clamp-2 break-words">
                  {project.project_name}
                </div>
              </h3>
            </TooltipTrigger>
            <TooltipContent side="top" className="max-w-xs">
              <p>{project.project_name}</p>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* SECTION 2: Recruiter/Vermittler Info - FIXED HEIGHT 6rem mit mehr Abstand */}
        <div className="h-24 px-4 py-2 flex flex-col justify-start">
          <div className="space-y-2 w-full pr-16">
            {/* Firmenname */}
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Building2 className="h-4 w-4 flex-shrink-0" />
              <span className="font-medium text-foreground" title={project.company_name}>
                {project.company_name}
              </span>
            </div>

            {/* Kontaktperson */}
            {project.contact_person && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <User className="h-4 w-4 flex-shrink-0" />
                <span title={project.contact_person}>
                  {project.contact_person}
                </span>
              </div>
            )}

            {/* Kontakt Details */}
            <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 text-sm text-muted-foreground">
              {project.contact_email && (
                <div className="flex items-center gap-1 min-w-0">
                  <Mail className="h-3 w-3 flex-shrink-0" />
                  <a
                    href={`mailto:${project.contact_email}`}
                    className="hover:text-primary transition-colors truncate"
                    title={project.contact_email}
                  >
                    {project.contact_email}
                  </a>
                </div>
              )}
              {project.contact_phone && (
                <div className="flex items-center gap-1 min-w-0">
                  <Phone className="h-3 w-3 flex-shrink-0" />
                  <span className="truncate" title={project.contact_phone}>
                    {project.contact_phone}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* SECTION 3: Work Location Badge - FIXED HEIGHT 2rem */}
        <div className="h-8 px-4 flex items-center">
          {project.work_location_type && (
            <Badge variant="outline" className={`${WORK_LOCATION_COLORS[project.work_location_type]} text-xs`}>
              {WORK_LOCATION_LABELS[project.work_location_type]}
              {project.remote_percentage && ` (${project.remote_percentage}%)`}
            </Badge>
          )}
        </div>

        {/* SECTION 4: Application Date and Budget - FIXED HEIGHT 3rem ohne Truncation */}
        <div className="h-12 px-4 flex items-center">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 text-sm w-full pr-16">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Calendar className="h-4 w-4 flex-shrink-0" />
              <span className="break-words" title={`Bewerbung: ${formatDate(project.application_date)}`}>
                Bewerbung: {formatDate(project.application_date)}
              </span>
            </div>
            <div className="text-muted-foreground">
              {project.budget_range && (
                <span className="break-words" title={`Budget: ${project.budget_range}`}>
                  <span className="font-medium">Budget:</span> {project.budget_range}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* SECTION 5: Project Dates - FIXED HEIGHT 2.5rem */}
        <div className="h-10 px-4 flex items-center">
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 text-sm text-muted-foreground w-full pr-16">
            {project.project_start_date && (
              <span className="break-words" title={`Start: ${formatDate(project.project_start_date)}`}>
                Start: {formatDate(project.project_start_date)}
              </span>
            )}
            {project.project_end_date && (
              <span className="break-words" title={`Ende: ${formatDate(project.project_end_date)}`}>
                Ende: {formatDate(project.project_end_date)}
              </span>
            )}
          </div>
        </div>

        {/* SECTION 6: Skills - FIXED HEIGHT 3rem */}
        <div className="h-12 px-4 flex items-center">
          <div className="w-full pr-16 overflow-hidden">
            {project.required_skills && project.required_skills.length > 0 && (
              <div className="flex flex-wrap gap-1 overflow-hidden">
                {project.required_skills.slice(0, 4).map((skill, index) => (
                  <Badge key={index} variant="secondary" className="text-xs whitespace-nowrap">
                    {skill}
                  </Badge>
                ))}
                {project.required_skills.length > 4 && (
                  <Badge 
                    variant="outline" 
                    className="text-xs cursor-pointer hover:bg-accent whitespace-nowrap"
                    title={`Alle Skills: ${project.required_skills.join(', ')}`}
                  >
                    +{project.required_skills.length - 4}
                  </Badge>
                )}
              </div>
            )}
          </div>
        </div>



        {/* SECTION 7: Project Description - FLEXIBLE HEIGHT (fills remaining space) */}
        <div className="flex-1 px-4 pb-4 flex flex-col">
          {project.project_description && (
            <div className="text-sm text-muted-foreground flex-1 pr-16">
              <div className="overflow-hidden">
                <p 
                  className={`${!isExpanded ? 'line-clamp-3' : ''} break-words`}
                  title={!isExpanded ? project.project_description : undefined}
                >
                  {project.project_description}
                </p>
              </div>
              {project.project_description.length > 150 && (
                <Button
                  variant="link"
                  size="sm"
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="h-auto p-0 text-xs text-primary mt-1"
                >
                  {isExpanded ? 'Weniger anzeigen' : 'Mehr anzeigen'}
                </Button>
              )}
            </div>
          )}
        </div>

        </div>
      </Card>
    </TooltipProvider>
  );
};
