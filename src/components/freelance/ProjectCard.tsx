import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { FreelanceProject, STATUS_LABELS, STATUS_COLORS, WORK_LOCATION_LABELS, WORK_LOCATION_COLORS } from '@/types/freelance';
import { Calendar, Building2, User, Mail, Phone, Edit, Trash2, Eye } from 'lucide-react';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';

interface ProjectCardProps {
  project: FreelanceProject;
  onEdit: (project: FreelanceProject) => void;
  onDelete: (id: string) => void;
  onView: (project: FreelanceProject) => void;
}

export const ProjectCard = ({ project, onEdit, onDelete, onView }: ProjectCardProps) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Nicht angegeben';
    try {
      return format(new Date(dateString), 'dd.MM.yyyy', { locale: de });
    } catch {
      return 'Ungültiges Datum';
    }
  };

  return (
    <TooltipProvider>
      <Card className="bg-gradient-card border-border/50 hover:border-primary/30 transition-all duration-300 hover:shadow-glow group w-full h-full flex flex-col">
        <div className="flex flex-col h-full relative">

          {/* Status Badge - Absolute Position */}
          <Badge
            variant="outline"
            className={`${STATUS_COLORS[project.status]} text-xs border absolute top-4 right-4 z-20`}
          >
            {STATUS_LABELS[project.status]}
          </Badge>

          {/* Action Buttons - Absolute Position */}
          <div className="flex gap-1 opacity-100 sm:opacity-0 sm:group-hover:opacity-100 transition-opacity absolute top-12 right-4 z-10">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onView(project)}
              className="h-7 w-7 sm:h-8 sm:w-8 p-0 hover:bg-primary/10"
            >
              <Eye className="h-3 w-3 sm:h-4 sm:w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEdit(project)}
              className="h-7 w-7 sm:h-8 sm:w-8 p-0 hover:bg-primary/10"
            >
              <Edit className="h-3 w-3 sm:h-4 sm:w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDelete(project.id)}
              className="h-7 w-7 sm:h-8 sm:w-8 p-0 hover:bg-destructive/10 text-destructive"
            >
              <Trash2 className="h-3 w-3 sm:h-4 sm:w-4" />
            </Button>
          </div>

          {/* SECTION 1: Project Title Header - Fixed Height for Consistent Alignment */}
          <div className="px-6 pt-16 h-24 flex items-start">
            <Tooltip>
              <TooltipTrigger asChild>
                <h3 className="font-semibold text-lg text-foreground group-hover:text-primary transition-colors leading-tight cursor-help pr-20 line-clamp-2">
                  {project.project_name}
                </h3>
              </TooltipTrigger>
              <TooltipContent side="top" className="max-w-xs">
                <p>{project.project_name}</p>
              </TooltipContent>
            </Tooltip>
          </div>

          {/* SECTION 2: Contact & Company Information - Consistent Position */}
          <div className="px-6 pb-4">
            <div className="bg-muted/20 rounded-lg p-4 border border-border/30">
              <div className="flex items-center gap-2 mb-4">
                <Building2 className="h-4 w-4 text-primary flex-shrink-0" />
                <span className="text-sm font-medium text-foreground">
                  Kontakt & Unternehmen
                </span>
              </div>

              <div className="space-y-3">
                {/* Company Name */}
                <div className="font-semibold text-foreground" title={project.company_name}>
                  {project.company_name}
                </div>

                {/* Contact Person */}
                {project.contact_person && (
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                    <span className="text-sm text-muted-foreground" title={project.contact_person}>
                      {project.contact_person}
                    </span>
                  </div>
                )}

                {/* Contact Details */}
                <div className="grid grid-cols-1 gap-2">
                  {project.contact_email && (
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                      <a
                        href={`mailto:${project.contact_email}`}
                        className="text-sm text-muted-foreground hover:text-primary transition-colors break-all"
                        title={project.contact_email}
                      >
                        {project.contact_email}
                      </a>
                    </div>
                  )}
                  {project.contact_phone && (
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                      <span className="text-sm text-muted-foreground" title={project.contact_phone}>
                        {project.contact_phone}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* SECTION 3: Project Details */}
          <div className="px-6 pb-4">
            <div className="bg-muted/20 rounded-lg p-4 border border-border/30">
              <div className="flex items-center gap-2 mb-4">
                <Calendar className="h-4 w-4 text-primary flex-shrink-0" />
                <span className="text-sm font-medium text-foreground">
                  Projektdetails
                </span>
              </div>

              <div className="space-y-3">
                {/* Application Date */}
                <div className="text-sm text-muted-foreground" title={`Bewerbung: ${formatDate(project.application_date)}`}>
                  <span className="font-medium text-foreground">Bewerbung:</span> {formatDate(project.application_date)}
                </div>

                {/* Work Location */}
                {project.work_location_type && (
                  <div className="text-sm text-muted-foreground">
                    <span className="font-medium text-foreground">Arbeitsort:</span> {WORK_LOCATION_LABELS[project.work_location_type]}
                    {project.remote_percentage && ` (${project.remote_percentage}%)`}
                  </div>
                )}

                {/* Project Start Date */}
                {project.project_start_date && (
                  <div className="text-sm text-muted-foreground" title={`Start: ${formatDate(project.project_start_date)}`}>
                    <span className="font-medium text-foreground">Start:</span> {formatDate(project.project_start_date)}
                  </div>
                )}

                {/* Project End Date */}
                {project.project_end_date && (
                  <div className="text-sm text-muted-foreground" title={`Ende: ${formatDate(project.project_end_date)}`}>
                    <span className="font-medium text-foreground">Ende:</span> {formatDate(project.project_end_date)}
                  </div>
                )}

                {/* Budget */}
                {project.budget_range && (
                  <div className="text-sm text-muted-foreground" title={`Budget: ${project.budget_range}`}>
                    <span className="font-medium text-foreground">Budget:</span> {project.budget_range}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* SECTION 4: Skills */}
          {project.required_skills && project.required_skills.length > 0 && (
            <div className="px-6 pb-4">
              <div className="flex flex-wrap gap-2">
                {project.required_skills.slice(0, 6).map((skill, index) => (
                  <Badge key={index} variant="secondary" className="text-sm">
                    {skill}
                  </Badge>
                ))}
                {project.required_skills.length > 6 && (
                  <Badge
                    variant="outline"
                    className="text-sm cursor-pointer hover:bg-accent"
                    title={`Alle Skills: ${project.required_skills.join(', ')}`}
                  >
                    +{project.required_skills.length - 6}
                  </Badge>
                )}
              </div>
            </div>
          )}

          {/* SECTION 5: Project Description */}
          {project.project_description && (
            <div className="flex-1 px-6 pb-6">
              <div className="bg-muted/10 rounded-lg p-4 border border-border/20">
                <div className="text-sm text-muted-foreground">
                  <p
                    className={`${!isExpanded ? 'line-clamp-3' : ''} break-words leading-relaxed`}
                    title={!isExpanded ? project.project_description : undefined}
                  >
                    {project.project_description}
                  </p>
                  {project.project_description.length > 150 && (
                    <Button
                      variant="link"
                      size="sm"
                      onClick={() => setIsExpanded(!isExpanded)}
                      className="h-auto p-0 text-sm text-primary mt-2"
                    >
                      {isExpanded ? 'Weniger anzeigen' : 'Mehr anzeigen'}
                    </Button>
                  )}
                </div>
              </div>
            </div>
          )}

        </div>
      </Card>
    </TooltipProvider>
  );
};
